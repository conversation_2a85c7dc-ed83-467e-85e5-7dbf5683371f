<!--实物资产档案-->
<template>
  <div class="buildings-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-office-building" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总房屋数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>间</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">房屋总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline" label-width="80px">
        <el-form-item label="房屋名称:">
          <el-input v-model="searchForm.zchiAssetsNo" placeholder="请输入房屋名称" class="inputW" />
        </el-form-item>
        <el-form-item label="产权证号:">
          <el-input v-model="searchForm.zchiCertificateCode" placeholder="请输入产权证号" class="inputW" />
        </el-form-item>
        <el-form-item label="产权单位:">
          <el-select v-model="searchForm.companyName" placeholder="请输入产权单位" clearable filterable class="inputW">
            <el-option label="选项1" value="选项1" />
          </el-select>
        </el-form-item>
        <el-form-item label="资产负责人:">
          <el-select v-model="searchForm.zchiOperator" placeholder="房屋" class="inputW" clearable>
            <el-option label="房屋" value="房屋" />
          </el-select>
        </el-form-item>
        <el-form-item label="所在城市:">
          <el-select v-model="searchForm.zchiCity" placeholder="南京" class="inputW" clearable>
            <el-option label="南京" value="南京" />
          </el-select>
        </el-form-item>
        <el-form-item label="资产能力:">
          <el-input v-model="searchForm.zchiBusinessDirection" placeholder="请输入资产能力" class="inputW" />
        </el-form-item>
        <el-form-item label="现状/用途:">
          <el-select v-model="searchForm.zchiUseDescribe" placeholder="科研办公" class="inputW" clearable>
            <el-option label="科研办公" value="科研办公" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否处置置:">
          <el-select v-model="searchForm.zchiIfDispose" placeholder="是" class="inputW" clearable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" :height="280" border style="width: 100%;margin-bottom: 20px;" v-loading="loading" row-key="zchiId">
      <el-table-column type="index" label="序号" width="60" align="center"/>
      <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiUseDescribe" label="现状用途" width="120" show-overflow-tooltip />
      <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip />
      <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" />
      <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip />
      <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" />
      <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip />
      <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip />
      <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip />
      <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip />
      <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip />
      <el-table-column prop="createdTime" label="创建时间" width="160" show-overflow-tooltip />
      <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip />
      <el-table-column prop="updatedTime" label="更新时间" width="160" show-overflow-tooltip />
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" background />

    <!-- 详情弹窗组件 -->
    <building-detail-dialog :visible.sync="dialogVisible" :detail-data="detailData" />
  </div>
</template>

<script>
import { getBuildingsList, getBuildingDetail, exportBuildings, getBuildingsStats } from '@/api/buildings'
import BuildingDetailDialog from './components/BuildingDetailDialog.vue'

export default {
  name: "index",
  components: {
    BuildingDetailDialog
  },
  data () {
    return {
      searchForm: {
        zchiAssetsNo: '',
        zchiCertificateCode: '',
        companyName: '',
        zchiOperator: '',
        zchiCity: '',
        zchiBusinessDirection: '',
        zchiUseDescribe: '',
        zchiIfDispose: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        disposalCount: 0,
        leaseCount: 0
      },
      dialogVisible: false,
      detailData: {}
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }

      getBuildingsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getBuildingsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.totalCount || 0,
            totalValue: response.data.totalValue || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.currentPage = 1
      this.fetchData()
    },

    onExport () {
      const query = {
        ...this.searchForm
      }
      exportBuildings(query).then(response => {
        // 处理导出逻辑
        this.$message.success('导出成功')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },

    handleDetail (row) {
      this.loading = true
      getBuildingDetail(row.zchiId).then(response => {
        if (response && response.data) {
          this.detailData = response.data
          this.dialogVisible = true
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 200px;
}

.buildings-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.data-table {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
