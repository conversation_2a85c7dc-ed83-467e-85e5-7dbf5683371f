import request from '@/utils/request'

// 查询设备列表
export function getDevicesList(query) {
  return request({
    url: '/zcgl-fixed-assets/vPageList',
    method: 'get',
    params: query
  })
}

// 查询设备详情
export function getDeviceDetail(id) {
  return request({
    url: `/zcgl-fixed-assets/get-by-id/${id}`,
    method: 'get'
  })
}

// 导出设备数据
export function exportDevices(query) {
  return request({
    url: '/zcgl-fixed-assets/vExport',
    method: 'post',
    data: query
  })
}

// 统计设备数量和价值
export function getDevicesStats() {
  return request({
    url: '/zcgl-fixed-assets/vStat',
    method: 'get'
  })
}
