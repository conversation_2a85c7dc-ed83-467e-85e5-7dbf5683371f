<!--设备资产管理页面-->
<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-container">
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="el-icon-s-data" style="color: #409EFF;"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">设备总数</div>
            <div class="stats-value">{{ statsData.totalCount || 0 }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="el-icon-s-check" style="color: #67C23A;"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">正常设备</div>
            <div class="stats-value">{{ statsData.normalCount || 0 }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="el-icon-warning" style="color: #E6A23C;"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">闲置设备</div>
            <div class="stats-value">{{ statsData.idleCount || 0 }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon">
            <i class="el-icon-money" style="color: #F56C6C;"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">总价值(万元)</div>
            <div class="stats-value">{{ statsData.totalValue || 0 }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <div class="filter-container">
      <el-form :model="listQuery" :inline="true" class="search-form">
        <el-form-item label="资产名称">
          <el-input v-model="listQuery.zcfaAssetsName" placeholder="请输入资产名称" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item label="产权单位">
          <el-input v-model="listQuery.companyName" placeholder="请输入产权单位" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item label="资产状态">
          <el-select v-model="listQuery.zcfaAssetsState" placeholder="请选择资产状态" style="width: 150px;" clearable>
            <el-option label="正常" value="正常" />
            <el-option label="闲置" value="闲置" />
            <el-option label="报废" value="报废" />
            <el-option label="维修" value="维修" />
          </el-select>
        </el-form-item>
        <el-form-item label="资产类型">
          <el-select v-model="listQuery.zcfaType" placeholder="请选择资产类型" style="width: 150px;" clearable>
            <el-option label="设备" value="设备" />
            <el-option label="仪器" value="仪器" />
            <el-option label="工具" value="工具" />
          </el-select>
        </el-form-item>
        <el-form-item label="制造厂商">
          <el-input v-model="listQuery.zcfaManufactor" placeholder="请输入制造厂商" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item label="型号">
          <el-input v-model="listQuery.zcfaModel" placeholder="请输入型号" style="width: 150px;" clearable />
        </el-form-item>
        <el-form-item label="生产日期">
          <el-date-picker v-model="productDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;" @change="handleProductDateChange" />
        </el-form-item>
        <el-form-item label="入账日期">
          <el-date-picker v-model="recordDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;" @change="handleRecordDateChange" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            重置
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="handleExport">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :key="tableKey" v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="资产名称" prop="zcfaAssetsName" min-width="150" show-overflow-tooltip>
        <template slot-scope="{row}">
          <el-button type="text" @click="handleDetail(row)">
            {{ row.zcfaAssetsName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="产权单位" prop="companyName" min-width="120" show-overflow-tooltip />
      <el-table-column label="资产状态" prop="zcfaAssetsState" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusTagType(row.zcfaAssetsState)" size="small">
            {{ row.zcfaAssetsState }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="资产类型" prop="zcfaType" width="100" align="center" />
      <el-table-column label="制造厂商" prop="zcfaManufactor" min-width="120" show-overflow-tooltip />
      <el-table-column label="型号" prop="zcfaModel" min-width="120" show-overflow-tooltip />
      <el-table-column label="序号" prop="zcfaSerialNumber" width="100" align="center" />
      <el-table-column label="账面原值(万元)" prop="zcfaBookValue" width="130" align="right">
        <template slot-scope="{row}">
          {{ row.zcfaBookValue ? Number(row.zcfaBookValue).toFixed(2) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="账面净值(万元)" prop="zcfaNetbookValue" width="130" align="right">
        <template slot-scope="{row}">
          {{ row.zcfaNetbookValue ? Number(row.zcfaNetbookValue).toFixed(2) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="生产日期" prop="zcfaProductDate" width="110" align="center">
        <template slot-scope="{row}">
          {{ row.zcfaProductDate ? row.zcfaProductDate.split(' ')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="入账日期" prop="zcfaRecordDate" width="110" align="center">
        <template slot-scope="{row}">
          {{ row.zcfaRecordDate ? row.zcfaRecordDate.split(' ')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="存放地点" prop="zcfaStoragePlace" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" background />

    <!-- 详情弹窗 -->
    <device-detail-dialog :visible.sync="detailDialogVisible" :detail-data="currentDetail" />
  </div>
</template>

<script>
import { getDevicesList, getDeviceDetail, exportDevices, getDevicesStats } from '@/api/device'
import DeviceDetailDialog from './components/DeviceDetailDialog'

export default {
  name: 'Device',
  components: {
    DeviceDetailDialog
  },
  data () {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      currentPage: 1,
      pageSize: 10,
      listQuery: {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: ''
      },
      productDateRange: [],
      recordDateRange: [],
      statsData: {},
      detailDialogVisible: false,
      currentDetail: {}
    }
  },
  created () {
    this.getList()
    this.getStats()
  },
  methods: {
    getList () {
      this.listLoading = true
      const query = {
        ...this.listQuery,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }
      getDevicesList(query).then(response => {
        this.list = response.data.list || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    getStats () {
      getDevicesStats().then(response => {
        this.statsData = response.data || {}
      })
    },
    handleFilter () {
      this.currentPage = 1
      this.getList()
    },
    resetQuery () {
      this.currentPage = 1
      this.listQuery = {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: ''
      }
      this.productDateRange = []
      this.recordDateRange = []
      this.getList()
    },
    handleProductDateChange (val) {
      if (val && val.length === 2) {
        this.listQuery.zcfaProductDateStart = val[0]
        this.listQuery.zcfaProductDateEnd = val[1]
      } else {
        this.listQuery.zcfaProductDateStart = ''
        this.listQuery.zcfaProductDateEnd = ''
      }
    },
    handleRecordDateChange (val) {
      if (val && val.length === 2) {
        this.listQuery.zcfaRecordDateStart = val[0]
        this.listQuery.zcfaRecordDateEnd = val[1]
      } else {
        this.listQuery.zcfaRecordDateStart = ''
        this.listQuery.zcfaRecordDateEnd = ''
      }
    },
    handleExport () {
      this.$confirm('确认导出设备数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportDevices(this.listQuery).then(() => {
          this.$message.success('导出成功')
        })
      })
    },
    handleDetail (row) {
      getDeviceDetail(row.zcfaId).then(response => {
        this.currentDetail = response.data || {}
        this.detailDialogVisible = true
      })
    },
    sortChange (data) {
      const { prop, order } = data
      if (prop === 'zcfaAssetsName') {
        this.sortByName(order)
      }
    },
    sortByName (order) {
      if (order === 'ascending') {
        this.listQuery.sortField = 'zcfaAssetsName'
        this.listQuery.sortOrder = 'asc'
      } else {
        this.listQuery.sortField = 'zcfaAssetsName'
        this.listQuery.sortOrder = 'desc'
      }
      this.handleFilter()
    },
    getStatusTagType (status) {
      const statusMap = {
        '正常': 'success',
        '闲置': 'warning',
        '报废': 'danger',
        '维修': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getList()
    }
  }
}
</script>

<style scoped>
.stats-container {
  margin-bottom: 20px;
}

.stats-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  height: 80px;
}

.stats-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stats-content {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.filter-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 0;
}

.search-form .el-form-item {
  margin-bottom: 15px;
}

.app-container {
  padding: 20px;
}
</style>
